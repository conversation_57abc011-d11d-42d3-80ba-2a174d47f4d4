import React, { useState, useContext, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
// import { Toast } from "Components/Toast";
import { showToast, GlobalContext } from "Context/Global";
import { AuthContext } from "Context/Auth";
const Logo = () => (
<svg width="30" height="24" viewBox="0 0 30 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Frame">
    <path id="Vector" d="M15.1594 3.99375L10.6219 7.66875C9.86719 8.27812 9.72188 9.375 10.2938 10.1578C10.8984 10.9922 12.075 11.1562 12.8859 10.5234L17.5406 6.90469C17.8688 6.65156 18.3375 6.70781 18.5953 7.03594C18.8531 7.36406 18.7922 7.83281 18.4641 8.09062L17.4844 8.85L24 14.85V6H23.9672L23.7844 5.88281L20.3812 3.70312C19.6641 3.24375 18.825 3 17.9719 3C16.95 3 15.9562 3.35156 15.1594 3.99375ZM16.2281 9.825L13.8047 11.7094C12.3281 12.8625 10.1859 12.5625 9.07969 11.0437C8.03906 9.61406 8.30156 7.61719 9.675 6.50625L13.575 3.35156C13.0312 3.12187 12.4453 3.00469 11.85 3.00469C10.9688 3 10.1109 3.2625 9.375 3.75L6 6V16.5H7.32187L11.6063 20.4094C12.525 21.2484 13.9453 21.1828 14.7844 20.2641C15.0422 19.9781 15.2156 19.6453 15.3047 19.2984L16.1016 20.0297C17.0156 20.8687 18.4406 20.8078 19.2797 19.8937C19.4906 19.6641 19.6453 19.3969 19.7438 19.1203C20.6531 19.7297 21.8906 19.6031 22.6547 18.7687C23.4937 17.8547 23.4328 16.4297 22.5187 15.5906L16.2281 9.825ZM0.75 6C0.3375 6 0 6.3375 0 6.75V16.5C0 17.3297 0.670312 18 1.5 18H3C3.82969 18 4.5 17.3297 4.5 16.5V6H0.75ZM2.25 15C2.44891 15 2.63968 15.079 2.78033 15.2197C2.92098 15.3603 3 15.5511 3 15.75C3 15.9489 2.92098 16.1397 2.78033 16.2803C2.63968 16.421 2.44891 16.5 2.25 16.5C2.05109 16.5 1.86032 16.421 1.71967 16.2803C1.57902 16.1397 1.5 15.9489 1.5 15.75C1.5 15.5511 1.57902 15.3603 1.71967 15.2197C1.86032 15.079 2.05109 15 2.25 15ZM25.5 6V16.5C25.5 17.3297 26.1703 18 27 18H28.5C29.3297 18 30 17.3297 30 16.5V6.75C30 6.3375 29.6625 6 29.25 6H25.5ZM27 15.75C27 15.5511 27.079 15.3603 27.2197 15.2197C27.3603 15.079 27.5511 15 27.75 15C27.9489 15 28.1397 15.079 28.2803 15.2197C28.421 15.3603 28.5 15.5511 28.5 15.75C28.5 15.9489 28.421 16.1397 28.2803 16.2803C28.1397 16.421 27.9489 16.5 27.75 16.5C27.5511 16.5 27.3603 16.421 27.2197 16.2803C27.079 16.1397 27 15.9489 27 15.75Z" fill="#7DD87D"/>
    </g>
  </svg>
);

const UserSignUpPage = () => {
  const schema = yup.object({
    first_name: yup.string().required("First name is required"),
    last_name: yup.string().required("Last name is required"),
    email: yup.string().email("Invalid email").required("Email is required"),
    password: yup.string()
      .min(8, "Password must be at least 8 characters")
      .required("Password is required"),
    confirm_password: yup.string()
      .oneOf([yup.ref('password')], 'Passwords must match')
      .required("Please confirm your password"),
    user_type: yup.array()
      .min(1, "Please select your role")
      .max(1, "Please select only one role")
      .required("Please select your role"),
    industry: yup.string().required("Please select your industry")
  });

  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [industries, setIndustries] = useState([]);
  const { dispatch } = useContext(AuthContext);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const params = new URLSearchParams(window.location.search);
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      user_type: []
    }
  });

  useEffect(() => {
    loadIndustries();
  }, []);

  const loadIndustries = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI("/v1/api/dealmaker/industries", {}, "GET");

      if (!response.error && response.data) {
        setIndustries(response.data);
      } else {
        showToast(globalDispatch, response.message || "Failed to load industries", 5000, "error");
      }
    } catch (err) {
      showToast(globalDispatch, err.message || "Failed to load industries", 5000, "error");
    }
  };

  const handleCheckboxChange = (value) => {
    setValue('user_type', [value], { shouldValidate: true });
  };

  const onSubmit = async (data) => {
    console.log("Form data:", data); // Debug log
    try {
      setIsLoading(true);
      setError("");

      const sdk = new MkdSDK();
      const ref = params.get("ref");
      const community_id = params.get("community");
      const response = await sdk.register({...data,referral_code:ref,community_id:community_id});

      if (!response.error) {
        if (response.token) {
          localStorage.setItem("token", response.token);
          localStorage.setItem("role", "member");
          dispatch({
            type: "LOGIN",
            payload: {
              user: response.user_id,
              token: response.token,
              role: "member"
            }
          });
          if (response.community_id) {
            navigate(`/member/communities?join_id=${response.community_id}`);
          } else {
            navigate("/member/dashboard");
          }
          showToast(globalDispatch, "Registration successful! Please login to continue.", 5000, "success");
        } else {
          navigate("/member/login", {
            state: {
              message: "Registration successful! Please login to continue."
            }
          });
        }
      }
    } catch (err) {
      console.error("Registration error:", err); // Debug log
      showToast(globalDispatch, err.message || "Registration failed. Please try again.", 5000, "error");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full min-h-screen bg-[#1E1E1E] flex flex-col">
      {/* Header */}
      <header style={{ marginBottom: "20px" }} className="flex justify-between px-[5vw]  bg-[#161616] h-[62px] items-center py-3">
        <div className="flex items-center gap-2">
          <Logo />
          <span className="text-[16px] font-bold text-[#EAEAEA]">RainmakerOS</span>
        </div>
        <div className="flex items-center gap-[2rem]">
          <a href="/member/login" className="text-[16px] text-[#eaeaea] hover:text-[#7dd87d]">
            Home
          </a>
          {/* <a href="/contact" className="text-[16px] text-[#eaeaea] hover:text-[#7dd87d]">
            Contact Us
          </a>
          <a href="/about" className="text-[16px] text-[#eaeaea] hover:text-[#7dd87d]">
            About Us
          </a> */}
          <a href="/member/signup" className="text-[16px] text-[#eaeaea] hover:text-[#7dd87d]">
            Sign Up
          </a>
        </div>
      </header>

      {/* Sign Up Form */}
      <div className="flex-1 bg-[#1E1E1E] flex flex-col items-center w-full">
        <div style={{
        width: "896px"
      }} className="space-y-4 ">
          <div className="text-center mb-8">
            <h2 className="text-[36px] font-bold text-[#EAEAEA]">Join Rain Maker</h2>
            <p className="mt-1 text-[18px] text-[#B5B5B5]">Create your account to start connecting</p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 bg-black p-[2rem] mx-auto mt-[1rem]">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-[16px] text-[#EAEAEA] mb-1">First Name</label>
                <input
                  type="text"
                  {...register("first_name")}
                  className="w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]"
                  placeholder="Enter your first name"
                />
                {errors.first_name && (
                  <p className="mt-1 text-xs text-red-500">{errors.first_name.message}</p>
                )}
              </div>
              <div>
                <label className="block text-[16px] text-[#eaeaea] mb-1">Last Name</label>
                <input
                  type="text"
                  {...register("last_name")}
                  className="w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]"
                  placeholder="Enter your last name"
                />
                {errors.last_name && (
                  <p className="mt-1 text-xs text-red-500">{errors.last_name.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-[16px] text-[#eaeaea] mb-1">Email</label>
                <input
                  type="email"
                  {...register("email")}
                  autoComplete="off"
                  className="w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]"
                  placeholder="Enter your email"
                />
                {errors.email && (
                  <p className="mt-1 text-xs text-red-500">{errors.email.message}</p>
                )}
              </div>
              <div>
                <label className="block text-[16px] text-[#eaeaea] mb-1">Password</label>
                <input
                  type="password"
                  {...register("password")}
                  autoComplete="new-password"
                  className="w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]"
                  placeholder="Create password"
                />
                {errors.password && (
                  <p className="mt-1 text-xs text-red-500">{errors.password.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-[16px] text-[#eaeaea] mb-2">I am:</label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register("user_type")}
                      value="opportunity"
                      onChange={(e) => handleCheckboxChange("opportunity")}
                      checked={watch("user_type").includes("opportunity")}
                      className="h-4 w-4 rounded bg-white text-[#2e7d32] focus:ring-0 border-0"
                    />
                    <span className="ml-2 text-[16px] text-[#ADAEBC]">Looking for Opportunity</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register("user_type")}
                      value="referrals"
                      onChange={(e) => handleCheckboxChange("referrals")}
                      checked={watch("user_type").includes("referrals")}
                      className="h-4 w-4 rounded bg-white text-[#2e7d32] focus:ring-0 border-0"
                    />
                    <span className="ml-2 text-[16px] text-[#ADAEBC]">I Have Referrals for Others</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      {...register("user_type")}
                      value="both"
                      onChange={(e) => handleCheckboxChange("both")}
                      checked={watch("user_type").includes("both")}
                      className="h-4 w-4 rounded bg-white text-[#2e7d32] focus:ring-0 border-0"
                    />
                    <span className="ml-2 text-[16px] text-[#ADAEBC]">I Am Looking for Both</span>
                  </label>
                </div>
                {errors.user_type && (
                  <p className="mt-1 text-xs text-red-500">{errors.user_type.message}</p>
                )}
              </div>
              <div className="flex flex-col space-y-4">
                <div>
                  <label className="block text-[16px] text-[#eaeaea] mb-1">Confirm Password</label>
                  <input
                    type="password"
                    {...register("confirm_password")}
                    autoComplete="new-password"
                    className="w-full h-[50px] rounded-[8px] mt-[0.5rem]border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]"
                    placeholder="Confirm password"
                  />
                  {errors.confirm_password && (
                    <p className="mt-1 text-xs text-red-500">{errors.confirm_password.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-[16px] text-[#eaeaea] mb-1">Industry</label>
                  <select
                    {...register("industry")}
                    className="w-full h-[50px] rounded-[8px] mt-[0.5rem]border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none appearance-none"
                  >
                    <option value="">Select your industry</option>
                    {industries.map((industry) => (
                      <option key={industry.id} value={industry.id}>
                        {industry.name}
                      </option>
                    ))}
                  </select>
                  {errors.industry && (
                    <p className="mt-1 text-xs text-red-500">{errors.industry.message}</p>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-center">
              <button
                type="submit"
                disabled={isLoading}
                className="w-[244px] h-[56px] rounded-[8px] mt-[2rem] bg-[#2e7d32] text-[16px] font-bold text-[#EAEAEA] hover:bg-[#266d2a] focus:outline-none disabled:opacity-50"
              >
                {isLoading ? "Creating Account..." : "Create Account"}
              </button>
            </div>
          </form>

          <div className="text-center ">
            <p className="text-[16px] text-[#b5b5b5] mt-[1rem]">
              Already have an account?{" "}
              <a href="/member/login" className="text-[#7dd87d] hover:text-[#6bc76b]">
                Login here
              </a>
            </p>
          </div>
        </div>
      </div>

      {/* {error && <Toast message={error} type="error" />} */}
    </div>
  );
};

export default UserSignUpPage;